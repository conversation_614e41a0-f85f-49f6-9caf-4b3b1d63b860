"""
AI生成图片自动数据集管理服务
负责将AI生成的图片自动添加到数据集中，包括元数据提取和标准化处理
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
from PIL import Image

from ..common.dataset_manager import DatasetManager
from ..common.json_template_manager import JSONTemplateManager

logger = logging.getLogger(__name__)

class AutoDatasetManager:
    """AI生成图片自动数据集管理器"""
    
    def __init__(self, dataset_base_dir: str = "data/datasets"):
        """
        初始化自动数据集管理器
        
        Args:
            dataset_base_dir: 数据集基础目录
        """
        self.dataset_manager = DatasetManager(dataset_base_dir)
        self.config = self._load_config()
        
        logger.info("AI生成图片自动数据集管理器初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        default_config = {
            "auto_add_to_dataset": True,
            "default_dataset_name": "AI_Generated_Images",
            "dataset_naming_format": "timestamp",  # "timestamp" 或 "sequential"
            "create_daily_datasets": True,  # 是否按日期创建数据集
            "max_images_per_dataset": 1000,  # 每个数据集最大图片数
            "auto_organize_by_target": False,  # 是否按军事目标自动分类
        }
        
        config_path = Path("config/auto_dataset_config.json")
        
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
                    logger.info("已加载用户配置")
            except Exception as e:
                logger.warning(f"加载配置文件失败，使用默认配置: {str(e)}")
        else:
            # 创建默认配置文件
            self._save_config(default_config)
        
        return default_config
    
    def _save_config(self, config: Dict[str, Any]):
        """保存配置"""
        config_path = Path("config/auto_dataset_config.json")
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            logger.info("配置已保存")
        except Exception as e:
            logger.error(f"保存配置失败: {str(e)}")
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return self.config.copy()
    
    def update_config(self, new_config: Dict[str, Any]) -> bool:
        """更新配置"""
        try:
            self.config.update(new_config)
            self._save_config(self.config)
            logger.info("配置更新成功")
            return True
        except Exception as e:
            logger.error(f"更新配置失败: {str(e)}")
            return False
    
    def get_or_create_default_dataset(self, military_target: Optional[str] = None) -> Optional[str]:
        """
        获取或创建默认数据集（每次都创建新的独立数据集）

        Args:
            military_target: 军事目标（用于分类数据集）

        Returns:
            str: 数据集文件夹名称，失败返回None
        """
        try:
            # 确定数据集名称
            dataset_name = self._determine_dataset_name(military_target)

            # 每次都创建新的数据集，确保独立性
            folder_name, folder_path = self.dataset_manager.create_dataset_folder(
                name=dataset_name,
                naming_format=self.config.get("dataset_naming_format", "timestamp")
            )

            logger.info(f"创建新的AI生成数据集: {folder_name}")
            return folder_name

        except Exception as e:
            logger.error(f"创建默认数据集失败: {str(e)}")
            return None
    
    def _determine_dataset_name(self, military_target: Optional[str] = None) -> str:
        """确定数据集名称"""
        base_name = self.config.get("default_dataset_name", "AI_Generated_Images")
        
        # 如果启用按目标分类
        if self.config.get("auto_organize_by_target", False) and military_target:
            base_name = f"{base_name}_{military_target}"
        
        # 如果启用按日期创建
        if self.config.get("create_daily_datasets", False):
            date_str = datetime.now().strftime("%Y%m%d")
            base_name = f"{base_name}_{date_str}"
        
        return base_name
    
    def _is_target_dataset(self, folder_name: str, target_name: str, military_target: Optional[str] = None) -> bool:
        """检查是否是目标数据集"""
        # 简单的名称匹配
        if target_name in folder_name:
            return True
        
        # 检查是否包含基础名称
        base_name = self.config.get("default_dataset_name", "AI_Generated_Images")
        if base_name in folder_name:
            # 如果启用按目标分类，检查目标匹配
            if self.config.get("auto_organize_by_target", False) and military_target:
                return military_target in folder_name
            return True
        
        return False
    
    def process_generated_image(
        self,
        image_path: str,
        generation_metadata: Dict[str, Any],
        generation_params: Dict[str, Any]
    ) -> bool:
        """
        处理生成的图片，自动添加到数据集
        
        Args:
            image_path: 生成的图片路径
            generation_metadata: 生成元数据（从file_manager保存的JSON）
            generation_params: 生成参数（从AI服务传入）
            
        Returns:
            bool: 是否成功处理
        """
        if not self.config.get("auto_add_to_dataset", True):
            logger.debug("自动添加到数据集功能已禁用")
            return True
        
        try:
            # 提取内容信息
            content_info = self._extract_content_info(generation_metadata, generation_params)
            military_target = content_info.get("military_target")
            
            # 获取或创建默认数据集
            dataset_folder = self.get_or_create_default_dataset(military_target)
            if not dataset_folder:
                logger.error("无法获取或创建默认数据集")
                return False
            
            # 提取和转换生成参数
            ai_generation_params = self._extract_ai_generation_params(generation_params)
            
            # 使用数据集管理器添加图片
            success = self.dataset_manager.add_generated_image_to_dataset(
                dataset_folder=dataset_folder,
                image_path=image_path,
                generation_type="ai_generation",
                generation_params=ai_generation_params,
                content_info=content_info
            )
            
            if success:
                logger.info(f"AI生成图片已自动添加到数据集: {dataset_folder}")
                return True
            else:
                logger.error("添加AI生成图片到数据集失败")
                return False
                
        except Exception as e:
            logger.error(f"处理生成图片失败: {str(e)}")
            return False
    
    def _extract_content_info(self, generation_metadata: Dict[str, Any], generation_params: Dict[str, Any]) -> Dict[str, Any]:
        """提取内容信息"""
        # 从元数据中提取
        metadata = generation_metadata.get("metadata", {})

        # 优先从generation_params获取信息，因为它更完整
        military_target = generation_params.get("military_target") or metadata.get("military_target")
        weather = generation_params.get("weather") or metadata.get("weather")
        scene = generation_params.get("scene") or metadata.get("scene")

        # 构建描述
        description_parts = ["AI生成"]
        if military_target:
            description_parts.append(f"{military_target}")
        if weather:
            description_parts.append(f"{weather}天气")
        if scene:
            description_parts.append(f"{scene}场景")
        description = "的".join(description_parts) + "图片"

        # 从提示词中提取更多信息
        prompt = generation_params.get("prompt", "")
        custom_prompt = generation_params.get("custom_prompt", "")

        # 构建标签
        tags = ["AI生成"]
        if military_target:
            tags.append(military_target)
        if weather:
            tags.append(weather)
        if scene:
            tags.append(scene)

        # 从提示词中提取额外标签
        if custom_prompt:
            tags.append("自定义提示词")

        # 根据生成参数添加技术标签
        steps = generation_params.get("steps", 0)
        if steps >= 50:
            tags.append("高质量")
        elif steps >= 30:
            tags.append("标准质量")

        cfg_scale = generation_params.get("cfg_scale", 0)
        if cfg_scale >= 10:
            tags.append("强引导")
        elif cfg_scale >= 7:
            tags.append("标准引导")

        content_info = {
            "description": description,
            "military_target": military_target,
            "weather": weather,
            "scene": scene,
            "tags": [tag for tag in tags if tag],  # 清理空标签
            "prompt_info": {
                "has_custom_prompt": bool(custom_prompt),
                "prompt_length": len(prompt),
                "custom_prompt_length": len(custom_prompt)
            }
        }

        return content_info
    
    def _extract_ai_generation_params(self, generation_params: Dict[str, Any]) -> Dict[str, Any]:
        """提取AI生成参数"""
        # 从生成参数中提取标准字段
        ai_params = {
            "model": generation_params.get("model", "stable-diffusion-v1-5"),
            "prompt": generation_params.get("prompt", ""),
            "negative_prompt": generation_params.get("negative_prompt", ""),
            "steps": generation_params.get("steps", 30),
            "cfg_scale": generation_params.get("cfg_scale", 7.5),
            "seed": generation_params.get("seed", -1),
            "sampler": generation_params.get("scheduler_name", "DPM++ 2M Karras"),
            "scheduler": generation_params.get("scheduler_name", "DPM++ 2M Karras"),
            "width": generation_params.get("width", 512),
            "height": generation_params.get("height", 512),
            "guidance_scale": generation_params.get("cfg_scale", 7.5),  # 别名

            # 高级参数
            "style_strength": generation_params.get("style_strength", 0.7),
            "technical_detail": generation_params.get("technical_detail", 0.8),
            "target_size_ratio": generation_params.get("target_size_ratio", 0.10),
            "num_images": generation_params.get("num_images", 1),
            "custom_prompt": generation_params.get("custom_prompt", ""),

            # 生成元信息
            "generation_id": generation_params.get("generation_id", ""),
            "image_index": generation_params.get("image_index", 0),
            "generation_time": datetime.now().isoformat(),

            # 计算衍生信息
            "aspect_ratio": self._calculate_aspect_ratio(
                generation_params.get("width", 512),
                generation_params.get("height", 512)
            ),
            "total_pixels": generation_params.get("width", 512) * generation_params.get("height", 512),
            "quality_level": self._determine_quality_level(generation_params),
            "complexity_score": self._calculate_complexity_score(generation_params)
        }

        return ai_params

    def _calculate_aspect_ratio(self, width: int, height: int) -> str:
        """计算宽高比"""
        if width == height:
            return "1:1"

        # 计算最大公约数
        def gcd(a, b):
            while b:
                a, b = b, a % b
            return a

        ratio_gcd = gcd(width, height)
        ratio_w = width // ratio_gcd
        ratio_h = height // ratio_gcd

        return f"{ratio_w}:{ratio_h}"

    def _determine_quality_level(self, params: Dict[str, Any]) -> str:
        """确定质量等级"""
        steps = params.get("steps", 30)
        cfg_scale = params.get("cfg_scale", 7.5)
        width = params.get("width", 512)
        height = params.get("height", 512)

        # 基于步数判断
        if steps >= 50:
            quality = "高"
        elif steps >= 30:
            quality = "中"
        else:
            quality = "低"

        # 基于分辨率调整
        total_pixels = width * height
        if total_pixels >= 1024 * 1024:  # 1MP+
            quality += "分辨率"

        return quality

    def _calculate_complexity_score(self, params: Dict[str, Any]) -> float:
        """计算复杂度分数"""
        score = 0.0

        # 步数贡献
        steps = params.get("steps", 30)
        score += min(steps / 50.0, 1.0) * 0.3

        # CFG比例贡献
        cfg_scale = params.get("cfg_scale", 7.5)
        score += min(cfg_scale / 15.0, 1.0) * 0.2

        # 分辨率贡献
        width = params.get("width", 512)
        height = params.get("height", 512)
        total_pixels = width * height
        score += min(total_pixels / (1024 * 1024), 1.0) * 0.2

        # 提示词复杂度贡献
        prompt = params.get("prompt", "")
        custom_prompt = params.get("custom_prompt", "")
        prompt_length = len(prompt) + len(custom_prompt)
        score += min(prompt_length / 500.0, 1.0) * 0.3

        return round(score, 3)
    
    def get_ai_generated_datasets(self) -> List[Dict[str, Any]]:
        """获取所有AI生成的数据集"""
        try:
            all_datasets = self.dataset_manager.list_datasets()
            base_name = self.config.get("default_dataset_name", "AI_Generated_Images")
            
            ai_datasets = []
            for dataset in all_datasets:
                folder_name = dataset.get("folder_name", "")
                if base_name in folder_name or "AI_Generated" in folder_name:
                    ai_datasets.append(dataset)
            
            return ai_datasets
            
        except Exception as e:
            logger.error(f"获取AI生成数据集失败: {str(e)}")
            return []
    
    def cleanup_old_datasets(self, max_datasets: int = 10) -> bool:
        """清理旧的数据集（保留最新的几个）"""
        try:
            ai_datasets = self.get_ai_generated_datasets()
            
            if len(ai_datasets) <= max_datasets:
                return True
            
            # 按创建时间排序
            ai_datasets.sort(key=lambda x: x.get("created_at", ""), reverse=True)
            
            # 删除多余的数据集
            datasets_to_delete = ai_datasets[max_datasets:]
            
            for dataset in datasets_to_delete:
                folder_name = dataset.get("folder_name")
                if folder_name:
                    success = self.dataset_manager.delete_dataset_folder(folder_name)
                    if success:
                        logger.info(f"已清理旧数据集: {folder_name}")
                    else:
                        logger.warning(f"清理数据集失败: {folder_name}")
            
            return True
            
        except Exception as e:
            logger.error(f"清理旧数据集失败: {str(e)}")
            return False

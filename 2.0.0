Requirement already satisfied: torchaudio in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (2.4.1+cu121)
Requirement already satisfied: torch==2.4.1+cu121 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from torchaudio) (2.4.1+cu121)
Requirement already satisfied: filelock in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from torch==2.4.1+cu121->torchaudio) (3.13.1)
Requirement already satisfied: typing-extensions>=4.8.0 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from torch==2.4.1+cu121->torchaudio) (4.12.2)
Requirement already satisfied: sympy in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from torch==2.4.1+cu121->torchaudio) (1.13.3)
Requirement already satisfied: networkx in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from torch==2.4.1+cu121->torchaudio) (3.0)
Requirement already satisfied: jinja2 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from torch==2.4.1+cu121->torchaudio) (3.1.4)
Requirement already satisfied: fsspec in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from torch==2.4.1+cu121->torchaudio) (2024.6.1)
Requirement already satisfied: MarkupSafe>=2.0 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from jinja2->torch==2.4.1+cu121->torchaudio) (2.1.5)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\users\<USER>\appdata\local\programs\python\python38\lib\site-packages (from sympy->torch==2.4.1+cu121->torchaudio) (1.3.0)
